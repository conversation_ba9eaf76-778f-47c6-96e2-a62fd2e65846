# Environment variables
.env
.env.local
.env.production

# Dependencies
node_modules/
frontend/node_modules/

# Build outputs
frontend/.nuxt/
frontend/.output/
frontend/dist/

# Logs
*.log
logs/
docker/logs/

# Database
*.sql
*.db
*.sqlite

# WordPress uploads and cache
wordpress_data/
mysql_data/
redis_data/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.docker/

# Temporary files
tmp/
temp/
*.tmp

# Backup files
*.bak
*.backup

# SSL certificates
*.pem
*.key
*.crt
docker/nginx/ssl/

# Cache directories
.cache/
.npm/
.yarn/

# Coverage reports
coverage/
*.lcov

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# WordPress specific
wp-config.php
wp-content/uploads/
wp-content/cache/
wp-content/backup-db/
wp-content/advanced-cache.php
wp-content/wp-cache-config.php
wp-content/cache/
wp-content/backups/

# Plugin and theme development
wp-content/plugins/hello.php
wp-content/plugins/akismet/
wp-content/themes/twenty*/

# Local development
local-config.php
.htaccess

# Composer
vendor/
composer.lock

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml
