#!/bin/bash

# Pet Store E-commerce Startup Script
# This script helps you get the pet store up and running quickly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if ports are available
check_ports() {
    local ports=(80 3000 3306 6379 8080)
    local occupied_ports=()
    
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -ne 0 ]; then
        print_warning "The following ports are occupied: ${occupied_ports[*]}"
        print_warning "Please stop the services using these ports or modify docker-compose.yml"
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        print_success "All required ports are available"
    fi
}

# Function to create .env file if it doesn't exist
setup_env() {
    if [ ! -f .env ]; then
        print_status "Creating .env file from .env.example..."
        cp .env.example .env
        print_success ".env file created"
    else
        print_success ".env file already exists"
    fi
}

# Function to build and start services
start_services() {
    print_status "Building and starting Docker services..."
    
    # Pull latest images
    docker-compose pull
    
    # Build and start services
    docker-compose up -d --build
    
    print_success "Docker services started"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    # Wait for MySQL
    print_status "Waiting for MySQL to be ready..."
    while ! docker-compose exec -T mysql mysqladmin ping -h localhost --silent; do
        sleep 2
    done
    print_success "MySQL is ready"
    
    # Wait for WordPress
    print_status "Waiting for WordPress to be ready..."
    local max_attempts=30
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost/wp/wp-admin/install.php > /dev/null 2>&1; then
            break
        fi
        sleep 5
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_warning "WordPress may not be fully ready yet. You can check manually."
    else
        print_success "WordPress is ready"
    fi
    
    # Wait for NuxtJS
    print_status "Waiting for NuxtJS to be ready..."
    local max_attempts=20
    local attempt=0
    
    while [ $attempt -lt $max_attempts ]; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            break
        fi
        sleep 3
        ((attempt++))
    done
    
    if [ $attempt -eq $max_attempts ]; then
        print_warning "NuxtJS may not be fully ready yet. You can check manually."
    else
        print_success "NuxtJS is ready"
    fi
}

# Function to install WordPress plugins and configure
setup_wordpress() {
    print_status "Setting up WordPress and installing plugins..."
    
    # Check if WordPress is installed
    if docker-compose exec -T wordpress wp core is-installed --allow-root 2>/dev/null; then
        print_success "WordPress is already installed"
    else
        print_status "Installing WordPress..."
        docker-compose exec wordpress wp core install \
            --url="http://localhost/wp" \
            --title="Pet Store - 宠物用品商店" \
            --admin_user="admin" \
            --admin_password="admin123" \
            --admin_email="<EMAIL>" \
            --allow-root
        print_success "WordPress installed"
    fi
    
    # Run plugin installation script
    if [ -f docker/wordpress/install-plugins.sh ]; then
        print_status "Installing and configuring plugins..."
        docker-compose exec wordpress bash /var/www/html/wp-content/plugins/custom/install-plugins.sh
        print_success "Plugins installed and configured"
    fi
}

# Function to install frontend dependencies
setup_frontend() {
    print_status "Setting up frontend dependencies..."
    
    if [ -d "frontend/node_modules" ]; then
        print_success "Frontend dependencies already installed"
    else
        print_status "Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
        print_success "Frontend dependencies installed"
    fi
}

# Function to display final information
show_info() {
    echo
    print_success "🎉 Pet Store E-commerce is ready!"
    echo
    echo "📱 Access URLs:"
    echo "   Frontend (NuxtJS):     http://localhost"
    echo "   WordPress Admin:       http://localhost/wp/wp-admin"
    echo "   phpMyAdmin:           http://localhost:8080"
    echo "   GraphQL Endpoint:     http://localhost/wp/graphql"
    echo
    echo "🔐 Default Credentials:"
    echo "   WordPress Admin:      admin / admin123"
    echo "   Database:            pet_store_user / pet_store_pass_2025"
    echo
    echo "📚 Useful Commands:"
    echo "   View logs:           docker-compose logs -f"
    echo "   Stop services:       docker-compose down"
    echo "   Restart services:    docker-compose restart"
    echo "   Update services:     docker-compose pull && docker-compose up -d"
    echo
    echo "🛠️  Development:"
    echo "   Frontend dev:        cd frontend && npm run dev"
    echo "   WordPress CLI:       docker-compose exec wordpress wp --allow-root"
    echo
    print_warning "Remember to change default passwords in production!"
}

# Main execution
main() {
    echo "🐾 Pet Store E-commerce Setup Script"
    echo "======================================"
    echo
    
    # Pre-flight checks
    check_docker
    check_ports
    
    # Setup
    setup_env
    setup_frontend
    start_services
    wait_for_services
    setup_wordpress
    
    # Final info
    show_info
}

# Handle script arguments
case "${1:-}" in
    "start")
        print_status "Starting services..."
        docker-compose up -d
        ;;
    "stop")
        print_status "Stopping services..."
        docker-compose down
        ;;
    "restart")
        print_status "Restarting services..."
        docker-compose restart
        ;;
    "logs")
        docker-compose logs -f
        ;;
    "clean")
        print_warning "This will remove all containers and volumes. Are you sure?"
        read -p "Continue? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v --remove-orphans
            docker system prune -f
            print_success "Cleanup completed"
        fi
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  (no args)  Full setup and start"
        echo "  start      Start services"
        echo "  stop       Stop services"
        echo "  restart    Restart services"
        echo "  logs       Show logs"
        echo "  clean      Clean up containers and volumes"
        echo "  help       Show this help"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
