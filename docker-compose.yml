version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: pet-store-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - pet-store-network
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: pet-store-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - pet-store-network
    command: redis-server --appendonly yes

  # WordPress Backend
  wordpress:
    image: wordpress:6.4-php8.2-fpm
    container_name: pet-store-wordpress
    restart: unless-stopped
    depends_on:
      - mysql
      - redis
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE}
      WORDPRESS_DB_USER: ${MYSQL_USER}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD}
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: ${WORDPRESS_DEBUG}
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_REDIS_HOST', 'redis');
        define('WP_REDIS_PORT', 6379);
        define('WP_REDIS_DATABASE', 0);
        define('GRAPHQL_JWT_AUTH_SECRET_KEY', '${JWT_SECRET_KEY}');
        define('WP_MEMORY_LIMIT', '512M');
    volumes:
      - wordpress_data:/var/www/html
      - ./docker/wordpress/uploads.ini:/usr/local/etc/php/conf.d/uploads.ini
      - ./docker/wordpress/plugins:/var/www/html/wp-content/plugins/custom
      - ./docker/wordpress/themes:/var/www/html/wp-content/themes/custom
    networks:
      - pet-store-network

  # NuxtJS Frontend
  nuxtjs:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: pet-store-nuxtjs
    restart: unless-stopped
    depends_on:
      - wordpress
    environment:
      NODE_ENV: ${NODE_ENV:-development}
      NUXT_PUBLIC_WORDPRESS_URL: http://nginx/wp
      NUXT_PUBLIC_GRAPHQL_URL: http://nginx/wp/graphql
      NUXT_PUBLIC_API_URL: http://nginx/wp/wp-json
      NUXT_PUBLIC_SITE_URL: ${SITE_URL}
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.nuxt
    ports:
      - "3000:3000"
      - "24678:24678" # HMR port
    networks:
      - pet-store-network
    command: npm run dev

  # Nginx Reverse Proxy
  nginx:
    image: nginx:1.25-alpine
    container_name: pet-store-nginx
    restart: unless-stopped
    depends_on:
      - wordpress
      - nuxtjs
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - wordpress_data:/var/www/html
      - ./docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - pet-store-network

  # phpMyAdmin (Development only)
  phpmyadmin:
    image: phpmyadmin:5.2
    container_name: pet-store-phpmyadmin
    restart: unless-stopped
    depends_on:
      - mysql
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: ${MYSQL_USER}
      PMA_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "8080:80"
    networks:
      - pet-store-network
    profiles:
      - development

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  wordpress_data:
    driver: local

networks:
  pet-store-network:
    driver: bridge
