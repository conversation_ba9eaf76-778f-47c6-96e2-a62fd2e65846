# Upstream servers
upstream wordpress_backend {
    server wordpress:9000;
}

upstream nuxtjs_frontend {
    server nuxtjs:3000;
}

# Main server block
server {
    listen 80;
    server_name localhost;
    root /var/www/html;
    index index.php index.html index.htm;

    # Security
    server_tokens off;

    # WordPress backend routes
    location ~ ^/wp {
        rewrite ^/wp/(.*)$ /$1 break;
        try_files $uri $uri/ /index.php?$args;
        
        # PHP processing
        location ~ \.php$ {
            try_files $uri =404;
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass wordpress_backend;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
            fastcgi_param HTTP_PROXY "";
            
            # Increase timeouts for admin operations
            fastcgi_read_timeout 300;
            fastcgi_connect_timeout 300;
            fastcgi_send_timeout 300;
        }
    }

    # WordPress GraphQL endpoint
    location ~ ^/wp/graphql {
        rewrite ^/wp/(.*)$ /$1 break;
        try_files $uri $uri/ /index.php?$args;
        
        location ~ \.php$ {
            fastcgi_pass wordpress_backend;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTP_PROXY "";
            
            # CORS headers for GraphQL
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
            
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
                add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With";
                add_header Access-Control-Max-Age 86400;
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 200;
            }
        }
        
        # Rate limiting for GraphQL
        limit_req zone=api burst=30 nodelay;
    }

    # WordPress API routes
    location ~ ^/wp/wp-json/ {
        rewrite ^/wp/(.*)$ /$1 break;
        try_files $uri $uri/ /index.php?$args;
        
        location ~ \.php$ {
            fastcgi_pass wordpress_backend;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param HTTP_PROXY "";
            
            # CORS headers for REST API
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With" always;
        }
        
        # Rate limiting for API
        limit_req zone=api burst=20 nodelay;
    }

    # WordPress admin login protection
    location ~ ^/wp/wp-login.php {
        rewrite ^/wp/(.*)$ /$1 break;
        limit_req zone=login burst=5 nodelay;

        fastcgi_pass wordpress_backend;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param HTTP_PROXY "";
    }

    # WordPress uploads and static files
    location ~ ^/wp/wp-content/uploads/ {
        rewrite ^/wp/(.*)$ /$1 break;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # WordPress static assets
    location ~ ^/wp/wp-content/.*\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        rewrite ^/wp/(.*)$ /$1 break;
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # NuxtJS frontend - all other routes
    location / {
        proxy_pass http://nuxtjs_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # WebSocket support for HMR
        proxy_read_timeout 86400;
    }

    # NuxtJS HMR WebSocket
    location /_nuxt/ {
        proxy_pass http://nuxtjs_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
