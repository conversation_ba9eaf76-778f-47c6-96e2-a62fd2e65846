// Global type definitions for Pet Store

export interface Product {
  id: number
  name: string
  slug: string
  description: string
  shortDescription?: string
  price: number
  regularPrice?: number
  salePrice?: number
  onSale: boolean
  inStock: boolean
  stockQuantity?: number
  sku: string
  weight?: string
  dimensions?: {
    length: string
    width: string
    height: string
  }
  categories: Category[]
  tags: Tag[]
  images: ProductImage[]
  attributes: ProductAttribute[]
  variations?: ProductVariation[]
  rating: number
  reviewCount: number
  featured: boolean
  virtual: boolean
  downloadable: boolean
  createdAt: string
  updatedAt: string
}

export interface ProductImage {
  id: number
  src: string
  alt: string
  name: string
}

export interface ProductAttribute {
  id: number
  name: string
  slug: string
  options: string[]
  variation: boolean
  visible: boolean
}

export interface ProductVariation {
  id: number
  price: number
  regularPrice?: number
  salePrice?: number
  onSale: boolean
  inStock: boolean
  stockQuantity?: number
  sku: string
  weight?: string
  dimensions?: {
    length: string
    width: string
    height: string
  }
  image?: ProductImage
  attributes: { [key: string]: string }
}

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  image?: string
  parent?: number
  count: number
}

export interface Tag {
  id: number
  name: string
  slug: string
  description?: string
  count: number
}

export interface CartItem {
  key: string
  productId: number
  variationId?: number
  quantity: number
  product: Product
  variation?: ProductVariation
  subtotal: number
  total: number
}

export interface Cart {
  items: CartItem[]
  itemsCount: number
  subtotal: number
  total: number
  totalTax: number
  shippingTotal: number
  discountTotal: number
  coupons: Coupon[]
  needsShipping: boolean
  needsPayment: boolean
}

export interface Coupon {
  id: number
  code: string
  amount: number
  discountType: 'percent' | 'fixed_cart' | 'fixed_product'
  description?: string
  expiryDate?: string
  usageLimit?: number
  usageCount: number
  individualUse: boolean
  productIds: number[]
  excludedProductIds: number[]
  categoryIds: number[]
  excludedCategoryIds: number[]
  minimumAmount?: number
  maximumAmount?: number
}

export interface Customer {
  id: number
  email: string
  firstName: string
  lastName: string
  displayName: string
  role: string
  billing: Address
  shipping: Address
  isPayingCustomer: boolean
  ordersCount: number
  totalSpent: number
  avatarUrl?: string
  dateCreated: string
  dateModified: string
}

export interface Address {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state: string
  postcode: string
  country: string
  email?: string
  phone?: string
}

export interface Order {
  id: number
  orderNumber: string
  status: OrderStatus
  currency: string
  dateCreated: string
  dateModified: string
  discountTotal: number
  shippingTotal: number
  shippingTax: number
  cartTax: number
  total: number
  totalTax: number
  customerId: number
  billing: Address
  shipping: Address
  paymentMethod: string
  paymentMethodTitle: string
  transactionId?: string
  customerNote?: string
  lineItems: OrderLineItem[]
  shippingLines: ShippingLine[]
  taxLines: TaxLine[]
  feeLines: FeeLine[]
  couponLines: CouponLine[]
}

export type OrderStatus = 
  | 'pending'
  | 'processing'
  | 'on-hold'
  | 'completed'
  | 'cancelled'
  | 'refunded'
  | 'failed'
  | 'trash'

export interface OrderLineItem {
  id: number
  name: string
  productId: number
  variationId?: number
  quantity: number
  taxClass: string
  subtotal: number
  subtotalTax: number
  total: number
  totalTax: number
  taxes: Tax[]
  metaData: MetaData[]
  sku?: string
  price: number
}

export interface ShippingLine {
  id: number
  methodTitle: string
  methodId: string
  total: number
  totalTax: number
  taxes: Tax[]
  metaData: MetaData[]
}

export interface TaxLine {
  id: number
  rateCode: string
  rateId: number
  label: string
  compound: boolean
  taxTotal: number
  shippingTaxTotal: number
  metaData: MetaData[]
}

export interface FeeLine {
  id: number
  name: string
  taxClass: string
  taxStatus: string
  total: number
  totalTax: number
  taxes: Tax[]
  metaData: MetaData[]
}

export interface CouponLine {
  id: number
  code: string
  discount: number
  discountTax: number
  metaData: MetaData[]
}

export interface Tax {
  id: number
  total: number
  subtotal: number
}

export interface MetaData {
  id: number
  key: string
  value: string
}

export interface Review {
  id: number
  dateCreated: string
  dateCreatedGmt: string
  productId: number
  status: string
  reviewer: string
  reviewerEmail: string
  review: string
  rating: number
  verified: boolean
  reviewerAvatarUrls: {
    [key: string]: string
  }
}

export interface ShippingZone {
  id: number
  name: string
  order: number
  locations: ShippingLocation[]
  methods: ShippingMethod[]
}

export interface ShippingLocation {
  code: string
  type: 'postcode' | 'state' | 'country' | 'continent'
}

export interface ShippingMethod {
  instanceId: number
  title: string
  order: number
  enabled: boolean
  methodId: string
  methodTitle: string
  methodDescription: string
  settings: { [key: string]: any }
}

export interface PaymentGateway {
  id: string
  title: string
  description: string
  order: number
  enabled: boolean
  methodTitle: string
  methodDescription: string
  methodSupports: string[]
  settings: { [key: string]: any }
}

// API Response types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  totalPages: number
  currentPage: number
  perPage: number
}

// Form types
export interface ContactForm {
  name: string
  email: string
  subject: string
  message: string
}

export interface NewsletterForm {
  email: string
  name?: string
}

export interface CheckoutForm {
  billing: Address
  shipping: Address
  shipToDifferentAddress: boolean
  paymentMethod: string
  customerNote?: string
  createAccount: boolean
  accountPassword?: string
  termsAccepted: boolean
}

// Store types (Pinia)
export interface CartState {
  items: CartItem[]
  isLoading: boolean
  error: string | null
}

export interface CustomerState {
  customer: Customer | null
  isLoggedIn: boolean
  isLoading: boolean
  error: string | null
}

export interface ProductState {
  products: Product[]
  categories: Category[]
  currentProduct: Product | null
  isLoading: boolean
  error: string | null
  filters: ProductFilters
}

export interface ProductFilters {
  category?: string
  tag?: string
  minPrice?: number
  maxPrice?: number
  onSale?: boolean
  inStock?: boolean
  featured?: boolean
  search?: string
  orderBy?: 'date' | 'title' | 'price' | 'popularity' | 'rating'
  order?: 'asc' | 'desc'
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
