<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <AppHeader />
    
    <!-- Main Content -->
    <main class="flex-1">
      <slot />
    </main>
    
    <!-- Footer -->
    <AppFooter />
    
    <!-- Loading Overlay -->
    <LoadingOverlay v-if="isLoading" />
    
    <!-- Toast Notifications -->
    <div id="toast-container"></div>
  </div>
</template>

<script setup lang="ts">
// Global loading state
const isLoading = ref(false)

// Provide loading state to child components
provide('isLoading', isLoading)

// Handle route changes
const route = useRoute()
watch(() => route.path, () => {
  // Reset scroll position on route change
  if (process.client) {
    window.scrollTo(0, 0)
  }
})
</script>
