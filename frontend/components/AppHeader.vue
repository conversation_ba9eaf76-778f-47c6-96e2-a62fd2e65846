<template>
  <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
    <!-- Top Bar -->
    <div class="bg-gray-900 text-white text-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <span>📞 客服热线: 400-123-4567</span>
            <span>📧 <EMAIL></span>
          </div>
          <div class="flex items-center space-x-4">
            <NuxtLink to="/account" class="hover:text-gray-300">我的账户</NuxtLink>
            <NuxtLink to="/wishlist" class="hover:text-gray-300">收藏夹</NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Header -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <NuxtLink to="/" class="flex items-center">
            <img class="h-8 w-auto" src="/logo.svg" alt="Pet Store" />
            <span class="ml-2 text-xl font-bold text-gray-900">Pet Store</span>
          </NuxtLink>
        </div>

        <!-- Search Bar -->
        <div class="flex-1 max-w-lg mx-8">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索产品..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              @keyup.enter="handleSearch"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
            </div>
          </div>
        </div>

        <!-- Navigation & Actions -->
        <div class="flex items-center space-x-4">
          <!-- Desktop Navigation -->
          <nav class="hidden md:flex space-x-8">
            <NuxtLink to="/" class="nav-link">首页</NuxtLink>
            <NuxtLink to="/products" class="nav-link">产品</NuxtLink>
            <NuxtLink to="/categories" class="nav-link">分类</NuxtLink>
            <NuxtLink to="/about" class="nav-link">关于我们</NuxtLink>
            <NuxtLink to="/contact" class="nav-link">联系我们</NuxtLink>
          </nav>

          <!-- Cart -->
          <button
            @click="toggleCart"
            class="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ShoppingCartIcon class="h-6 w-6" />
            <span
              v-if="cartItemsCount > 0"
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center"
            >
              {{ cartItemsCount }}
            </span>
          </button>

          <!-- Mobile Menu Button -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden p-2 text-gray-600 hover:text-gray-900"
          >
            <Bars3Icon v-if="!isMobileMenuOpen" class="h-6 w-6" />
            <XMarkIcon v-else class="h-6 w-6" />
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-if="isMobileMenuOpen" class="md:hidden bg-white border-t border-gray-200">
      <div class="px-4 py-2 space-y-1">
        <NuxtLink to="/" class="block px-3 py-2 text-gray-600 hover:text-gray-900">首页</NuxtLink>
        <NuxtLink to="/products" class="block px-3 py-2 text-gray-600 hover:text-gray-900">产品</NuxtLink>
        <NuxtLink to="/categories" class="block px-3 py-2 text-gray-600 hover:text-gray-900">分类</NuxtLink>
        <NuxtLink to="/about" class="block px-3 py-2 text-gray-600 hover:text-gray-900">关于我们</NuxtLink>
        <NuxtLink to="/contact" class="block px-3 py-2 text-gray-600 hover:text-gray-900">联系我们</NuxtLink>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { MagnifyingGlassIcon, ShoppingCartIcon, Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'

// Reactive state
const searchQuery = ref('')
const isMobileMenuOpen = ref(false)

// Cart state (from store)
const cartItemsCount = ref(0) // This should come from your cart store

// Methods
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/search?q=${encodeURIComponent(searchQuery.value)}`)
  }
}

const toggleCart = () => {
  // Toggle cart sidebar or navigate to cart page
  navigateTo('/cart')
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

// Close mobile menu on route change
const route = useRoute()
watch(() => route.path, () => {
  isMobileMenuOpen.value = false
})
</script>
