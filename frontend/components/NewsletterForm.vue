<template>
  <form @submit.prevent="handleSubmit" class="max-w-md mx-auto">
    <div class="flex flex-col sm:flex-row gap-4">
      <div class="flex-1">
        <input
          v-model="email"
          type="email"
          placeholder="输入您的邮箱地址"
          required
          class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent text-gray-900"
        />
      </div>
      <button
        type="submit"
        :disabled="isSubmitting"
        class="px-6 py-3 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span v-if="!isSubmitting">订阅</span>
        <span v-else>订阅中...</span>
      </button>
    </div>
    
    <p v-if="message" class="mt-4 text-sm" :class="messageType === 'success' ? 'text-green-200' : 'text-red-200'">
      {{ message }}
    </p>
    
    <p class="mt-4 text-sm text-blue-100">
      订阅我们的新闻通讯，获取最新的宠物护理技巧和优惠信息。
    </p>
  </form>
</template>

<script setup lang="ts">
// Reactive state
const email = ref('')
const isSubmitting = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

// Methods
const handleSubmit = async () => {
  if (!email.value) return

  isSubmitting.value = true
  message.value = ''

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Here you would make an actual API call to subscribe the user
    // const response = await $fetch('/api/newsletter/subscribe', {
    //   method: 'POST',
    //   body: { email: email.value }
    // })

    message.value = '订阅成功！感谢您的订阅。'
    messageType.value = 'success'
    email.value = ''
  } catch (error) {
    message.value = '订阅失败，请稍后重试。'
    messageType.value = 'error'
  } finally {
    isSubmitting.value = false
  }
}
</script>
