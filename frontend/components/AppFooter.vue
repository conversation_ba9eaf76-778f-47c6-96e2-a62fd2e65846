<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div>
          <div class="flex items-center mb-4">
            <img class="h-8 w-auto" src="/logo-white.svg" alt="Pet Store" />
            <span class="ml-2 text-xl font-bold">Pet Store</span>
          </div>
          <p class="text-gray-300 mb-4">
            为您的宠物提供最优质的产品和服务，让每一个毛孩子都能健康快乐地成长。
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <span class="sr-only">Facebook</span>
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <span class="sr-only">微信</span>
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 3.882-1.98 5.853-1.838-.576-3.583-4.196-6.348-8.596-6.348zM5.785 5.991c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.813 0c.642 0 1.162.529 1.162 1.18 0 .659-.52 1.188-1.162 1.188-.642 0-1.162-.529-1.162-1.188 0-.651.52-1.18 1.162-1.18zm5.34 2.867c-1.797-.052-3.746.512-5.28 1.786-1.72 1.428-2.687 3.72-1.78 6.22.942 2.453 3.666 4.229 6.884 4.229.826 0 1.622-.12 2.361-.336a.722.722 0 0 1 .598.082l1.584.926a.272.272 0 0 0 .14.045c.134 0 .24-.111.24-.248 0-.06-.023-.12-.038-.177l-.327-1.233a.582.582 0 0 1-.023-.156.49.49 0 0 1 .201-.398C23.024 18.48 24 16.82 24 14.98c0-3.21-2.931-5.837-6.062-6.122zm-3.12 4.069c.765 0 1.377.616 1.377 1.375 0 .76-.612 1.375-1.377 1.375-.765 0-1.377-.616-1.377-1.375 0-.759.612-1.375 1.377-1.375zm4.816 0c.765 0 1.377.616 1.377 1.375 0 .76-.612 1.375-1.377 1.375-.765 0-1.377-.616-1.377-1.375 0-.759.612-1.375 1.377-1.375z"/>
              </svg>
            </a>
            <a href="#" class="text-gray-400 hover:text-white transition-colors">
              <span class="sr-only">微博</span>
              <svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9.31 8.17c-2.77-.3-5.21.51-5.46 1.82-.25 1.3 1.79 2.84 4.56 3.43 2.77.59 5.21-.22 5.46-1.53.25-1.3-1.79-2.84-4.56-3.72zm-1.99 4.74c-.61.13-1.16-.17-1.22-.67-.06-.5.41-.99 1.02-1.12.61-.13 1.16.17 1.22.67.06.5-.41.99-1.02 1.12zm2.48-.57c-.23.05-.43-.06-.45-.24-.02-.18.15-.36.38-.41.23-.05.43.06.45.24.02.18-.15.36-.38.41z"/>
              </svg>
            </a>
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-lg font-semibold mb-4">快速链接</h3>
          <ul class="space-y-2">
            <li><NuxtLink to="/products" class="text-gray-300 hover:text-white transition-colors">所有产品</NuxtLink></li>
            <li><NuxtLink to="/categories/dogs" class="text-gray-300 hover:text-white transition-colors">狗狗用品</NuxtLink></li>
            <li><NuxtLink to="/categories/cats" class="text-gray-300 hover:text-white transition-colors">猫咪用品</NuxtLink></li>
            <li><NuxtLink to="/sale" class="text-gray-300 hover:text-white transition-colors">特价商品</NuxtLink></li>
            <li><NuxtLink to="/new-arrivals" class="text-gray-300 hover:text-white transition-colors">新品上架</NuxtLink></li>
          </ul>
        </div>

        <!-- Customer Service -->
        <div>
          <h3 class="text-lg font-semibold mb-4">客户服务</h3>
          <ul class="space-y-2">
            <li><NuxtLink to="/help" class="text-gray-300 hover:text-white transition-colors">帮助中心</NuxtLink></li>
            <li><NuxtLink to="/shipping" class="text-gray-300 hover:text-white transition-colors">配送信息</NuxtLink></li>
            <li><NuxtLink to="/returns" class="text-gray-300 hover:text-white transition-colors">退换货政策</NuxtLink></li>
            <li><NuxtLink to="/contact" class="text-gray-300 hover:text-white transition-colors">联系我们</NuxtLink></li>
            <li><NuxtLink to="/faq" class="text-gray-300 hover:text-white transition-colors">常见问题</NuxtLink></li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div>
          <h3 class="text-lg font-semibold mb-4">联系方式</h3>
          <div class="space-y-2 text-gray-300">
            <p class="flex items-center">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
              </svg>
              400-123-4567
            </p>
            <p class="flex items-center">
              <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <EMAIL>
            </p>
            <p class="flex items-start">
              <svg class="h-5 w-5 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              北京市朝阳区宠物大街123号
            </p>
            <p class="text-sm">
              营业时间: 周一至周日 9:00-21:00
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom Bar -->
    <div class="border-t border-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © 2025 Pet Store. 保留所有权利.
          </p>
          <div class="flex space-x-6 mt-2 md:mt-0">
            <NuxtLink to="/privacy" class="text-gray-400 hover:text-white text-sm transition-colors">隐私政策</NuxtLink>
            <NuxtLink to="/terms" class="text-gray-400 hover:text-white text-sm transition-colors">服务条款</NuxtLink>
            <NuxtLink to="/cookies" class="text-gray-400 hover:text-white text-sm transition-colors">Cookie政策</NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// Footer component logic can be added here
</script>
