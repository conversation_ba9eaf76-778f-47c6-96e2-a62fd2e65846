<template>
  <NuxtLink
    :to="`/categories/${category.slug}`"
    class="group block card-hover overflow-hidden"
  >
    <div class="aspect-w-16 aspect-h-9 bg-gray-200">
      <img
        :src="category.image"
        :alt="category.name"
        class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
        loading="lazy"
      />
    </div>
    <div class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
        {{ category.name }}
      </h3>
      <p class="text-sm text-gray-500 mt-1">
        {{ category.count }} 个产品
      </p>
    </div>
  </NuxtLink>
</template>

<script setup lang="ts">
interface Category {
  id: number
  name: string
  image: string
  slug: string
  count: number
}

defineProps<{
  category: Category
}>()
</script>
