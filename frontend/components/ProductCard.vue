<template>
  <div class="card-hover group">
    <div class="relative">
      <div class="aspect-w-1 aspect-h-1 bg-gray-200 rounded-t-lg overflow-hidden">
        <img
          :src="product.image"
          :alt="product.name"
          class="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
          loading="lazy"
        />
      </div>
      
      <!-- Quick Actions -->
      <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div class="flex flex-col space-y-2">
          <button
            @click="toggleWishlist"
            class="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors"
            :class="{ 'text-red-500': isInWishlist, 'text-gray-400': !isInWishlist }"
          >
            <HeartIcon class="h-5 w-5" :class="{ 'fill-current': isInWishlist }" />
          </button>
          <button
            @click="quickView"
            class="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors text-gray-400 hover:text-gray-600"
          >
            <EyeIcon class="h-5 w-5" />
          </button>
        </div>
      </div>

      <!-- Sale Badge -->
      <div v-if="product.onSale" class="absolute top-2 left-2">
        <span class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
          特价
        </span>
      </div>
    </div>

    <div class="p-4">
      <!-- Product Name -->
      <h3 class="text-lg font-medium text-gray-900 mb-2 line-clamp-2">
        <NuxtLink :to="`/products/${product.slug || product.id}`" class="hover:text-blue-600 transition-colors">
          {{ product.name }}
        </NuxtLink>
      </h3>

      <!-- Rating -->
      <div class="flex items-center mb-2">
        <div class="flex items-center">
          <StarIcon
            v-for="i in 5"
            :key="i"
            class="h-4 w-4"
            :class="i <= Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'"
          />
        </div>
        <span class="text-sm text-gray-500 ml-2">
          ({{ product.reviews }})
        </span>
      </div>

      <!-- Price -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-bold text-gray-900">
            ¥{{ product.price }}
          </span>
          <span v-if="product.originalPrice" class="text-sm text-gray-500 line-through">
            ¥{{ product.originalPrice }}
          </span>
        </div>
      </div>

      <!-- Add to Cart Button -->
      <button
        @click="addToCart"
        :disabled="!product.inStock"
        class="w-full btn-primary disabled:bg-gray-300 disabled:cursor-not-allowed"
      >
        <span v-if="product.inStock">加入购物车</span>
        <span v-else>缺货</span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { HeartIcon, EyeIcon, StarIcon } from '@heroicons/vue/24/outline'

interface Product {
  id: number
  name: string
  price: number
  originalPrice?: number
  image: string
  rating: number
  reviews: number
  slug?: string
  onSale?: boolean
  inStock?: boolean
}

const props = defineProps<{
  product: Product
}>()

// Reactive state
const isInWishlist = ref(false)

// Methods
const toggleWishlist = () => {
  isInWishlist.value = !isInWishlist.value
  // Add wishlist logic here
}

const quickView = () => {
  // Open quick view modal
  console.log('Quick view:', props.product.name)
}

const addToCart = () => {
  if (props.product.inStock !== false) {
    // Add to cart logic here
    console.log('Added to cart:', props.product.name)
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
