{"name": "pet-store-frontend", "version": "1.0.0", "description": "Pet Store E-commerce Frontend built with NuxtJS and WooCommerce", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host 0.0.0.0", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "nuxt typecheck"}, "dependencies": {"@nuxt/ui": "^2.18.7", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/tailwindcss": "^6.12.1", "@pinia/nuxt": "^0.5.5", "@vueuse/nuxt": "^11.1.0", "@apollo/client": "^3.11.8", "@vue/apollo-composable": "^4.2.1", "graphql": "^16.9.0", "graphql-tag": "^2.12.6", "nuxt": "^3.13.2", "pinia": "^2.2.4", "vue": "^3.5.12", "vue-router": "^4.4.5", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.1.5", "swiper": "^11.1.14", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@nuxt/devtools": "^1.5.2", "@nuxt/eslint": "^0.5.7", "@types/node": "^22.7.5", "autoprefixer": "^10.4.20", "eslint": "^9.12.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "^5.6.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}