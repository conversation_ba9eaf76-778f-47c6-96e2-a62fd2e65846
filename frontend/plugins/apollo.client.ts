import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client/core'
import { setContext } from '@apollo/client/link/context'
import { DefaultApolloClient } from '@vue/apollo-composable'

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()
  
  // HTTP Link
  const httpLink = createHttpLink({
    uri: config.public.graphqlUrl || 'http://localhost/wp/graphql',
    credentials: 'include'
  })

  // Auth Link
  const authLink = setContext((_, { headers }) => {
    // Get authentication token from cookie or localStorage
    const token = useCookie('apollo-token').value
    
    return {
      headers: {
        ...headers,
        authorization: token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json'
      }
    }
  })

  // Apollo Client
  const apolloClient = new ApolloClient({
    link: authLink.concat(httpLink),
    cache: new InMemoryCache({
      typePolicies: {
        Product: {
          fields: {
            reviews: {
              merge(existing = [], incoming) {
                return [...existing, ...incoming]
              }
            }
          }
        },
        Query: {
          fields: {
            products: {
              keyArgs: ['where'],
              merge(existing = { nodes: [] }, incoming) {
                return {
                  ...incoming,
                  nodes: [...existing.nodes, ...incoming.nodes]
                }
              }
            }
          }
        }
      }
    }),
    defaultOptions: {
      watchQuery: {
        errorPolicy: 'all'
      },
      query: {
        errorPolicy: 'all'
      }
    }
  })

  // Provide Apollo Client
  nuxtApp.vueApp.provide(DefaultApolloClient, apolloClient)
  
  return {
    provide: {
      apolloClient
    }
  }
})
