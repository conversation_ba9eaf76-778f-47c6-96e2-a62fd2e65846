<template>
  <div id="app">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
// Global app configuration
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' }
  ]
})

// Global error handling
const handleError = (error: any) => {
  console.error('Global error:', error)
  // You can add error reporting service here
}

// Set up global error handler
onErrorCaptured(handleError)
</script>

<style>
/* Global styles are imported in nuxt.config.ts */
</style>
