// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // CSS Framework
  css: ['~/assets/css/main.css'],
  
  // Modules
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/google-fonts',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxt/ui'
  ],

  // Runtime config
  runtimeConfig: {
    // Private keys (only available on server-side)
    woocommerceConsumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY,
    woocommerceConsumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET,
    
    // Public keys (exposed to client-side)
    public: {
      wordpressUrl: process.env.NUXT_PUBLIC_WORDPRESS_URL || 'http://localhost/wp',
      graphqlUrl: process.env.NUXT_PUBLIC_GRAPHQL_URL || 'http://localhost/wp/graphql',
      apiUrl: process.env.NUXT_PUBLIC_API_URL || 'http://localhost/wp/wp-json',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost',
      siteName: 'Pet Store',
      siteDescription: 'Your one-stop shop for all pet supplies and accessories'
    }
  },



  // Google Fonts
  googleFonts: {
    families: {
      Inter: [300, 400, 500, 600, 700, 800, 900],
      'Playfair Display': [400, 500, 600, 700, 800, 900]
    },
    display: 'swap',
    preload: true
  },

  // Tailwind CSS
  tailwindcss: {
    cssPath: '~/assets/css/main.css',
    configPath: 'tailwind.config.js',
    exposeConfig: false,
    viewer: true
  },

  // App configuration
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'Pet Store - Your Pet\'s Best Friend',
      meta: [
        { name: 'description', content: 'Your one-stop shop for all pet supplies and accessories' },
        { name: 'keywords', content: 'pet store, pet supplies, dog food, cat toys, pet accessories' },
        { property: 'og:type', content: 'website' },
        { property: 'og:title', content: 'Pet Store - Your Pet\'s Best Friend' },
        { property: 'og:description', content: 'Your one-stop shop for all pet supplies and accessories' },
        { name: 'twitter:card', content: 'summary_large_image' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },

  // Build configuration
  build: {
    transpile: ['@headlessui/vue', '@heroicons/vue']
  },

  // Server configuration
  nitro: {
    preset: 'node-server'
  },

  // Development server
  devServer: {
    host: '0.0.0.0',
    port: 3000
  },

  // TypeScript configuration
  typescript: {
    strict: true,
    typeCheck: true
  },

  // Experimental features
  experimental: {
    payloadExtraction: false
  },

  // Compatibility
  compatibilityDate: '2024-08-05'
})
