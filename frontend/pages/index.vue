<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-blue-600 to-purple-600 text-white">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold font-display mb-6">
            为您的宠物提供最好的
          </h1>
          <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
            发现优质的宠物用品、食品和玩具，让您的毛孩子健康快乐
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <NuxtLink to="/products" class="btn-primary text-lg px-8 py-3">
              立即购买
            </NuxtLink>
            <NuxtLink to="/about" class="btn-outline text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-gray-900">
              了解更多
            </NuxtLink>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Categories -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold font-display text-gray-900 mb-4">
            热门分类
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            为不同类型的宠物找到完美的产品
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <CategoryCard
            v-for="category in featuredCategories"
            :key="category.id"
            :category="category"
          />
        </div>
      </div>
    </section>

    <!-- Featured Products -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold font-display text-gray-900 mb-4">
            精选产品
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            我们为您精心挑选的优质宠物用品
          </p>
        </div>
        
        <div class="product-grid" v-if="!pending">
          <ProductCard
            v-for="product in featuredProducts"
            :key="product.id"
            :product="product"
          />
        </div>
        
        <div v-else class="flex justify-center">
          <div class="loading-spinner"></div>
        </div>
      </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-blue-600 text-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold font-display mb-4">
          订阅我们的新闻通讯
        </h2>
        <p class="text-xl mb-8">
          获取最新的宠物护理技巧和产品优惠信息
        </p>
        <NewsletterForm />
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// SEO
useHead({
  title: '宠物用品商店 - 为您的宠物提供最好的',
  meta: [
    { name: 'description', content: '发现优质的宠物用品、食品和玩具，让您的毛孩子健康快乐。我们提供全方位的宠物护理产品。' },
    { name: 'keywords', content: '宠物用品, 狗粮, 猫玩具, 宠物配件, 宠物护理' }
  ]
})

// Mock data for development
const featuredCategories = ref([
  {
    id: 1,
    name: '狗狗用品',
    image: '/images/categories/dogs.jpg',
    slug: 'dogs',
    count: 156
  },
  {
    id: 2,
    name: '猫咪用品',
    image: '/images/categories/cats.jpg',
    slug: 'cats',
    count: 98
  },
  {
    id: 3,
    name: '小动物用品',
    image: '/images/categories/small-pets.jpg',
    slug: 'small-pets',
    count: 45
  },
  {
    id: 4,
    name: '鸟类用品',
    image: '/images/categories/birds.jpg',
    slug: 'birds',
    count: 32
  }
])

// Fetch featured products
const { data: featuredProducts, pending } = await useLazyFetch('/api/products/featured', {
  default: () => [
    {
      id: 1,
      name: '优质狗粮 - 成犬配方',
      price: 299.99,
      image: '/images/products/dog-food-1.jpg',
      rating: 4.8,
      reviews: 124
    },
    {
      id: 2,
      name: '猫咪互动玩具套装',
      price: 89.99,
      image: '/images/products/cat-toys-1.jpg',
      rating: 4.6,
      reviews: 89
    },
    {
      id: 3,
      name: '宠物智能饮水器',
      price: 199.99,
      image: '/images/products/water-fountain-1.jpg',
      rating: 4.9,
      reviews: 156
    },
    {
      id: 4,
      name: '舒适宠物床垫',
      price: 159.99,
      image: '/images/products/pet-bed-1.jpg',
      rating: 4.7,
      reviews: 78
    }
  ]
})
</script>
