# Production Docker Compose Override
# Use with: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  # MySQL Production Configuration
  mysql:
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=512M
      --innodb-log-file-size=128M
      --max-connections=200
      --query-cache-size=64M
      --query-cache-type=1
      --slow-query-log=1
      --long-query-time=2
    ports: []  # Remove external port exposure

  # Redis Production Configuration
  redis:
    restart: always
    command: >
      redis-server
      --appendonly yes
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    ports: []  # Remove external port exposure

  # WordPress Production Configuration
  wordpress:
    restart: always
    environment:
      WORDPRESS_DB_HOST: mysql:3306
      WORDPRESS_DB_NAME: ${MYSQL_DATABASE}
      WORDPRESS_DB_USER: ${MYSQL_USER}
      WORDPRESS_DB_PASSWORD: ${MYSQL_PASSWORD}
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: false
      WORDPRESS_CONFIG_EXTRA: |
        define('WP_REDIS_HOST', 'redis');
        define('WP_REDIS_PORT', 6379);
        define('WP_REDIS_DATABASE', 0);
        define('GRAPHQL_JWT_AUTH_SECRET_KEY', '${JWT_SECRET_KEY}');
        define('WP_MEMORY_LIMIT', '512M');
        define('DISALLOW_FILE_EDIT', true);
        define('AUTOMATIC_UPDATER_DISABLED', true);
        define('WP_AUTO_UPDATE_CORE', false);
        define('FORCE_SSL_ADMIN', true);
        define('WP_CACHE', true);

  # NuxtJS Production Configuration
  nuxtjs:
    build:
      target: production
    restart: always
    environment:
      NODE_ENV: production
      NUXT_PUBLIC_WORDPRESS_URL: ${SITE_URL}/wp
      NUXT_PUBLIC_GRAPHQL_URL: ${SITE_URL}/wp/graphql
      NUXT_PUBLIC_API_URL: ${SITE_URL}/wp/wp-json
      NUXT_PUBLIC_SITE_URL: ${SITE_URL}
    volumes:
      - ./frontend:/app:ro  # Read-only in production
      - /app/node_modules
      - /app/.nuxt
    ports: []  # Remove external port exposure
    command: node .output/server/index.mjs

  # Nginx Production Configuration
  nginx:
    restart: always
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - wordpress_data:/var/www/html:ro
      - ./docker/nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"

  # Remove phpMyAdmin in production
  phpmyadmin:
    profiles:
      - debug  # Only available with --profile debug

# Additional production services
  # Log aggregation
  logrotate:
    image: linkyard/docker-logrotate
    container_name: pet-store-logrotate
    restart: unless-stopped
    volumes:
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/logs
    environment:
      LOGROTATE_CONF: |
        /logs/*.log {
          daily
          rotate 30
          compress
          delaycompress
          missingok
          notifempty
          create 0644 root root
        }
    networks:
      - pet-store-network

  # Monitoring (optional)
  watchtower:
    image: containrrr/watchtower
    container_name: pet-store-watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      WATCHTOWER_CLEANUP: true
      WATCHTOWER_POLL_INTERVAL: 86400  # Check daily
      WATCHTOWER_INCLUDE_STOPPED: true
    networks:
      - pet-store-network
    profiles:
      - monitoring

volumes:
  mysql_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/pet-store/mysql
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/pet-store/redis
  wordpress_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /var/lib/pet-store/wordpress
