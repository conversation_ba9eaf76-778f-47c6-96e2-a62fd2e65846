# Pet Store E-commerce - NuxtJS + WordPress

一个现代化的宠物用品电商网站，使用 NuxtJS 作为前端，WordPress + WooCommerce 作为后端 API。

## 🏗️ 技术栈

### 前端
- **NuxtJS 3.13** - Vue.js 全栈框架
- **Tailwind CSS 3.4** - 实用优先的 CSS 框架
- **Apollo GraphQL** - GraphQL 客户端
- **Pinia** - Vue 状态管理
- **TypeScript** - 类型安全

### 后端
- **WordPress 6.4** - 内容管理系统
- **WooCommerce** - 电商插件
- **WPGraphQL** - GraphQL API
- **MySQL 8.0** - 数据库
- **Redis 7** - 缓存系统

### 基础设施
- **Docker & Docker Compose** - 容器化部署
- **Nginx** - 反向代理和负载均衡
- **PHP 8.2** - 后端运行时

## 🚀 快速开始

### 前置要求
- Docker 和 Docker Compose
- Node.js 18+ (用于本地开发)
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd pet-ecommerce-store-V12.0
```

### 2. 环境配置
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量（可选，默认配置已可用）
nano .env
```

### 3. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 初始化 WordPress
```bash
# 等待服务启动完成（约2-3分钟）
# 然后运行 WordPress 初始化脚本
docker-compose exec wordpress bash /var/www/html/wp-content/plugins/custom/install-plugins.sh
```

### 5. 访问应用
- **前端网站**: http://localhost
- **WordPress 管理**: http://localhost/wp/wp-admin
- **phpMyAdmin**: http://localhost:8080 (开发环境)
- **GraphQL 端点**: http://localhost/wp/graphql

## 📁 项目结构

```
pet-ecommerce-store-V12.0/
├── frontend/                 # NuxtJS 前端应用
│   ├── components/          # Vue 组件
│   ├── pages/              # 页面路由
│   ├── layouts/            # 布局模板
│   ├── assets/             # 静态资源
│   ├── stores/             # Pinia 状态管理
│   ├── composables/        # 组合式函数
│   ├── types/              # TypeScript 类型定义
│   └── nuxt.config.ts      # Nuxt 配置
├── docker/                  # Docker 配置文件
│   ├── nginx/              # Nginx 配置
│   ├── wordpress/          # WordPress 配置
│   └── mysql/              # MySQL 初始化脚本
├── docker-compose.yml       # Docker Compose 配置
├── .env                    # 环境变量
└── README.md               # 项目文档
```

## 🔧 开发指南

### 前端开发
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### WordPress 开发
```bash
# 进入 WordPress 容器
docker-compose exec wordpress bash

# 使用 WP-CLI
wp --info --allow-root

# 安装插件
wp plugin install plugin-name --allow-root

# 更新数据库
wp db optimize --allow-root
```

### 数据库管理
```bash
# 连接到 MySQL
docker-compose exec mysql mysql -u pet_store_user -p pet_store_db

# 备份数据库
docker-compose exec mysql mysqldump -u root -p pet_store_db > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p pet_store_db < backup.sql
```

## 🛠️ 配置说明

### 环境变量
主要环境变量说明：

- `NODE_ENV`: 运行环境 (development/production)
- `MYSQL_*`: 数据库连接配置
- `WORDPRESS_DEBUG`: WordPress 调试模式
- `JWT_SECRET_KEY`: JWT 认证密钥
- `SITE_URL`: 网站访问地址

### WordPress 插件
自动安装的核心插件：

- **WooCommerce**: 电商功能
- **WPGraphQL**: GraphQL API
- **WPGraphQL WooCommerce**: WooCommerce GraphQL 扩展
- **Redis Cache**: Redis 缓存
- **Yoast SEO**: SEO 优化
- **Advanced Custom Fields**: 自定义字段

### API 端点
- REST API: `/wp/wp-json/wc/v3/`
- GraphQL: `/wp/graphql`
- 认证: JWT Token 认证

## 🔒 安全配置

### 生产环境安全检查清单
- [ ] 更改默认数据库密码
- [ ] 设置强 JWT 密钥
- [ ] 启用 HTTPS
- [ ] 配置防火墙规则
- [ ] 定期更新依赖
- [ ] 启用 WordPress 安全插件
- [ ] 配置备份策略

### 推荐的生产环境配置
```bash
# 生产环境启动
NODE_ENV=production docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 📊 性能优化

### 缓存策略
- **Redis**: 对象缓存和会话存储
- **Nginx**: 静态文件缓存
- **WordPress**: 页面缓存插件
- **CDN**: 建议使用 CDN 加速静态资源

### 数据库优化
- 定期清理无用数据
- 优化数据库索引
- 监控慢查询
- 配置合适的缓冲池大小

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :80
   
   # 重新构建容器
   docker-compose down
   docker-compose up --build -d
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose logs mysql
   
   # 重置数据库
   docker-compose down -v
   docker-compose up -d
   ```

3. **前端构建失败**
   ```bash
   # 清理缓存
   cd frontend
   rm -rf node_modules .nuxt
   npm install
   ```

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs nginx
docker-compose logs wordpress
docker-compose logs mysql
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或需要帮助，请：
- 创建 Issue
- 发送邮件至 <EMAIL>
- 查看文档 Wiki

---

**Pet Store E-commerce** - 为您的宠物提供最好的 🐾
