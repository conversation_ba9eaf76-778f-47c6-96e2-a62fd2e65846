# Environment Configuration
NODE_ENV=development
SITE_URL=http://localhost

# MySQL Database Configuration
MYSQL_ROOT_PASSWORD=root_password_change_me
MYSQL_DATABASE=pet_store_db
MYSQL_USER=pet_store_user
MYSQL_PASSWORD=pet_store_password_change_me

# WordPress Configuration
WORDPRESS_DEBUG=true
JWT_SECRET_KEY=your-super-secret-jwt-key-change-me

# WooCommerce Configuration
WOOCOMMERCE_CONSUMER_KEY=
WOOCOMMERCE_CONSUMER_SECRET=

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM_EMAIL=
SMTP_FROM_NAME=Pet Store

# Payment Gateway Configuration (Optional)
STRIPE_PUBLISHABLE_KEY=
STRIPE_SECRET_KEY=
PAYPAL_CLIENT_ID=
PAYPAL_CLIENT_SECRET=

# Social Media Integration (Optional)
FACEBOOK_APP_ID=
GOOGLE_ANALYTICS_ID=

# Security
WORDPRESS_AUTH_KEY=generate-unique-key-here
WORDPRESS_SECURE_AUTH_KEY=generate-unique-key-here
WORDPRESS_LOGGED_IN_KEY=generate-unique-key-here
WORDPRESS_NONCE_KEY=generate-unique-key-here
WORDPRESS_AUTH_SALT=generate-unique-salt-here
WORDPRESS_SECURE_AUTH_SALT=generate-unique-salt-here
WORDPRESS_LOGGED_IN_SALT=generate-unique-salt-here
WORDPRESS_NONCE_SALT=generate-unique-salt-here
